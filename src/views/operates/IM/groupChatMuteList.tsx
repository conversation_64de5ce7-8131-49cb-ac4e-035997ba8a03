import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import { Table } from '@components/common';
import { getCrumb } from '@utils/utils';
import {
  Button,
  Divider,
  Col,
  Form,
  Icon,
  Input,
  message,
  Modal,
  Row,
  Select,
  Timeline,
} from 'antd';
import { useRouteMatch } from 'react-router-dom';
import { CommonObject } from '@app/types';
import { setMenuHook } from '@app/utils/utils';
import { opApi } from '@app/api/opApi';
import useXHR from '@utils/useXhr';
import moment from 'moment';
import AddGroupChatMuteModal from './components/AddGroupChatMuteModal';

interface FilterState {
  keyword: string;
  status: string; // 'all' | '1' | '0'
}

interface MuteRecord {
  id: string;
  account_id: string;
  account_name: string;
  account_page: string;
  chao_id: string;
  status: number; // 0正常，1拉黑
  created_at: number;
  created_by: string;
  updated_by: string;
  updated_at: number;
}

interface MuteLogRecord {
  id: string;
  operate_type: 'mute' | 'unmute';
  operate_time: string;
  operator_name: string;
  reason?: string;
}

export default function GroupChatMuteList(props: any) {
  const dispatch = useDispatch();
  const match = useRouteMatch();

  // Redux state
  const tableList = useSelector((state: any) => state.tableList);
  const tableCache = useSelector((state: any) => state.tableCache);
  const { current, size, records = [] } = useSelector((state: any) => state.tableList);

  // Local state
  const [filter, setFilter] = useState<FilterState>({
    keyword: '',
    status: 'all',
  });

  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [loading, setLoading] = useState(false);
  const { loading: xhrLoading, run } = useXHR();

  // 搜索状态
  const [search, setSearch] = useState<{
    keyword: string;
  }>({
    keyword: '',
  });

  // 操作日志弹窗 - 复用私信黑名单的格式
  const [logs, setLogs] = useState<any>({
    visible: false,
    logs: [],
    title: '',
  });

  // 添加禁言弹窗状态
  const [addMuteModal, setAddMuteModal] = useState({
    visible: false,
  });

  // 初始化
  useEffect(() => {
    setMenuHook(dispatch, props);

    if (tableCache?.beforeRoute === match?.path && tableCache.records.length > 0) {
      getData({ current: tableCache.current, size: tableCache.size });
    } else {
      getData({ current: 1 });
    }

    setIsInitialized(true);
  }, []);

  // 获取数据
  const getData = (overlap: CommonObject = {}) => {
    const params = {
      ...getFilter(),
      ...overlap,
      chat_type: 1 // ✅ 添加默认参数
    };
    console.log('获取群聊禁言列表数据', params);
    dispatch(getTableList('getGroupChatMuteList', 'list', params));
  };

  // 获取过滤条件
  const getFilter = () => {
    const { current, size } = tableList;
    const filters: CommonObject = { current, size };

    // 添加所有筛选字段
    Object.keys(filter).forEach((key) => {
      const value = filter[key as keyof typeof filter];
      if (value !== '' && value !== 'all' && value !== undefined) {
        filters[key] = value;
      }
    });

    return filters;
  };

  // 处理过滤条件变化
  const handleFilterChange = (key: string, value: any) => {
    setFilter({
      ...filter,
      [key]: value,
    });
  };

  // 监听 filter 变化
  useEffect(() => {
    if (isInitialized) {
      getData({ current: 1 });
    }
  }, [filter]);

  // 处理搜索
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // 处理搜索
  const handleSearch = () => {
    setFilter({
      ...filter,
      keyword: search.keyword,
    });
  };

  // ✅ 添加禁言功能 - 仅方法定义，不实现UI
  const addMuteUser = (accountId: string, reason?: string) => {
    console.log('添加禁言用户', { accountId, reason });
    // ✅ API调用预留
    // api.addMuteUser({ account_id: accountId, reason }).then(() => {
    //   message.success('禁言成功');
    //   getData();
    // });
  };

  // 处理添加禁用账号按钮点击
  const handleAddMuteUser = () => {
    console.log('打开添加禁用账号弹窗');
    setAddMuteModal({ visible: true });
  };

  // 处理添加禁言弹窗确定
  const handleAddMuteModalOk = (values: any) => {
    console.log('添加禁言账号', values);
    // ✅ API调用预留
    // api.addGroupChatMuteUser(values).then(() => {
    //   message.success('操作成功');
    //   getData();
    // });

    // 刷新列表数据
    getData();
  };

  // 处理添加禁言弹窗取消
  const handleAddMuteModalCancel = () => {
    setAddMuteModal({ visible: false });
  };

  // 获取已禁言用户ID列表（用于重复检查）
  const getExistingMuteUsers = (): string[] => {
    return records
      .filter((record: MuteRecord) => record.status === 1)
      .map((record: MuteRecord) => record.account_id);
  };

  // 取消禁言
  const handleUnmute = (record: MuteRecord) => {
    Modal.confirm({
      title: '确定取消拉黑吗？',
      content: `将取消用户"${record.account_name}"的拉黑状态`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        console.log('取消拉黑', record);
        setLoading(true);
        return opApi.cancelBlackUser({ id: record.id }).then(() => {
          message.success('取消拉黑成功');
          getData();
        }).catch(() => {
          // 不显示错误提示，按要求处理
        }).finally(() => {
          setLoading(false);
        });
      },
    });
  };

  // 获取操作日志 - 复用私信黑名单的实现
  const showOperationRecord = (record: MuteRecord) => {
    run(opApi.getOperateLog, { type: 170, target_id: record.id }, true)
      .then((res: any) => {
        setLogs({
          visible: true,
          logs: res?.data?.admin_log_list || [],
          title: record.account_name,
        });
      })
      .catch(() => {});
  };

  // 显示用户详情
  const showUserDetail = (record: MuteRecord) => {
    console.log('显示用户详情', record);
    // ✅ 这里可以调用用户详情弹窗或跳转到用户详情页面
    // 参考 rated.tsx 中的 showUserDetailModal 方法
  };

  // 处理批量选择
  const handleSelectChange = (selectedKeys: any[]) => {
    setSelectedRowKeys(selectedKeys);
  };

  // 获取序号
  const getSeq = (i: number) => (current - 1) * size + i + 1;

  // 格式化时间戳
  const formatTime = (timestamp: number) => {
    if (!timestamp) return '-';
    return new Date(timestamp * 1000).toLocaleString('zh-CN');
  };

  // 获取列配置
  const columns = [
    {
      title: '序号',
      key: 'seq',
      render: (_: any, __: any, i: number) => <span>{getSeq(i)}</span>,
      width: 60,
    },
    {
      title: '账号昵称',
      dataIndex: 'account_name',
      key: 'account_name',
      width: 150,
      render: (text: string, record: MuteRecord) => (
        <a onClick={() => showUserDetail(record)}>{text}</a>
      ),
    },
    {
      title: '用户主页',
      dataIndex: 'account_page',
      key: 'account_page',
      width: 150,
    },
    {
      title: '小潮号',
      dataIndex: 'chao_id',
      key: 'chao_id',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: number) => (
        <span style={{ color: status === 1 ? '#f5222d' : '#52c41a' }}>
          {status === 1 ? '拉黑' : '正常'}
        </span>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 160,
      render: (timestamp: number) => formatTime(timestamp),
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
      key: 'created_by',
      width: 100,
    },
    {
      title: '操作',
      key: 'op',
      width: 150,
      fixed: 'right',
      render: (_: any, record: MuteRecord) => (
        <span>
          {record.status === 1 && (
            <>
              <a
                onClick={() => !loading && handleUnmute(record)}
                style={{ color: loading ? '#ccc' : undefined }}
              >
                取消拉黑
              </a>
              <Divider type="vertical" />
            </>
          )}
          <a onClick={() => showOperationRecord(record)}>查看记录</a>
        </span>
      ),
    },
  ];

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button
            type="primary"
            onClick={handleAddMuteUser}
            style={{ marginRight: 8 }}
          >
            <Icon type="plus-circle" /> 添加禁言账号
          </Button>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>

      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={16}>
            <Form layout="inline">
              <Form.Item>
                <Select
                  value={filter.status}
                  onChange={(value) => handleFilterChange('status', value)}
                  style={{ width: 120 }}
                  placeholder="全部状态"
                >
                  <Select.Option value="all">全部状态</Select.Option>
                  <Select.Option value="1">拉黑</Select.Option>
                  <Select.Option value="0">正常</Select.Option>
                </Select>
              </Form.Item>
            </Form>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
              <Input
                value={search.keyword}
                onChange={(e) => setSearch({ ...search, keyword: e.target.value })}
                placeholder="请输入账号昵称或小潮号"
                style={{ width: 200, marginRight: 8 }}
                onKeyDown={handleKeyDown}
              />
              <Button type="primary" onClick={handleSearch}>
                <Icon type="search" /> 搜索
              </Button>
            </div>
          </Col>
        </Row>

        <Table
          func="getGroupChatMuteList"
          index="list"
          rowKey="id"
          filter={getFilter()}
          columns={columns}
          pagination={true}
          multi={false}
          selectedRowKeys={selectedRowKeys}
          onSelectChange={handleSelectChange}
          tableProps={{ scroll: { x: 800 } }}
        />

        {/* 操作日志弹窗 - 复用私信黑名单的实现 */}
        <Modal
          visible={logs.visible}
          title="操作日志"
          key={logs.key}
          cancelText={null}
          onCancel={() => setLogs({ ...logs, visible: false })}
          onOk={() => setLogs({ ...logs, visible: false })}
        >
          <div>
            <Timeline>
              {logs.logs?.map((v: any, i: number) => [
                <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                  &nbsp;
                </Timeline.Item>,
                v.log_list?.map((action: any, index: number) => (
                  <Timeline.Item
                    className="timeline-dot"
                    data-show={moment(action.created_at).format('HH:mm:ss')}
                    key={`time${i}-action${index}`}
                  >
                    {action.admin_name}&emsp;{action.remark}
                  </Timeline.Item>
                )),
              ])}
            </Timeline>
          </div>
        </Modal>

        {/* 添加群聊禁言账号弹窗 */}
        <AddGroupChatMuteModal
          visible={addMuteModal.visible}
          onCancel={handleAddMuteModalCancel}
          onOk={handleAddMuteModalOk}
          existingMuteUsers={getExistingMuteUsers()}
        />
      </div>
    </>
  );
}
